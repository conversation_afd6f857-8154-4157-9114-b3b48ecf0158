using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using HappyWechat.Infrastructure.MessageQueue.Simplified.Consumers;
using HappyWechat.Infrastructure.MessageQueue.Simplified.Pipeline;
using HappyWechat.Infrastructure.MessageQueue.Simplified.Pipeline.Handlers;
using HappyWechat.Application.Interfaces;
using HappyWechat.Infrastructure.Services;
using HappyWechat.Infrastructure.Integration;
using HappyWechat.Infrastructure.MessageProcessing;
using HappyWechat.Infrastructure.Monitoring;
using HappyWechat.Infrastructure.ConfigSync;
using HappyWechat.Infrastructure.MessageQueue.Redis;
// using HappyWechat.Infrastructure.MessageQueue.Pipeline; // Old pipeline removed

namespace HappyWechat.Infrastructure.MessageQueue.Simplified;

/// <summary>
/// 简化队列服务注册扩展 - 完全替代旧架构
/// 注册所有新架构的服务和消费者
/// </summary>
public static class SimplifiedServiceExtensions
{
    /// <summary>
    /// 注册简化的消息队列服务 - 完整的新架构实现
    /// </summary>
    public static IServiceCollection AddSimplifiedMessageQueue(this IServiceCollection services)
    {
        // 核心队列服务
        services.AddSingleton<ISimplifiedQueueService, SimplifiedQueueService>();
        
        // 消息管道编排器 - 替代旧的MessagePipelineOrchestrator
        services.AddScoped<ISimplifiedMessagePipelineOrchestrator, SimplifiedMessagePipelineOrchestrator>();
        
        // 管道处理器 - 替代旧的所有Handler
        services.AddScoped<SimplifiedAccountConfigHandler>();
        services.AddScoped<SimplifiedSensitiveWordHandler>();
        services.AddScoped<SimplifiedFriendRequestHandler>();
        services.AddScoped<SimplifiedGroupInviteHandler>();
        services.AddScoped<SimplifiedMessageTypeHandler>();
        // SimplifiedAiReplyHandler已删除，使用新的AI处理流程
        services.AddScoped<SimplifiedFileProcessHandler>();
        
        // 🚀 流式架构：革命性的非阻塞消息处理系统（完全替代所有旧消费者）
        services.AddSingleton<HappyWechat.Infrastructure.MessageQueue.Streaming.StreamingMessageArchitecture>();
        services.AddHostedService<HappyWechat.Infrastructure.MessageQueue.Streaming.StreamingMessageArchitecture>(provider => 
            provider.GetRequiredService<HappyWechat.Infrastructure.MessageQueue.Streaming.StreamingMessageArchitecture>());
        
        // 🚀 流式架构：智能消息路由器 - 毫秒级响应，永不阻塞
        services.AddScoped<HappyWechat.Infrastructure.MessageQueue.Streaming.IIntelligentMessageRouter,
            HappyWechat.Infrastructure.MessageQueue.Streaming.IntelligentMessageRouter>();
            
        // 🚀 流式架构：媒体预处理器 - 解决大文件阻塞问题
        services.AddScoped<HappyWechat.Infrastructure.MessageQueue.Streaming.IMediaPreprocessor,
            HappyWechat.Infrastructure.MessageQueue.Streaming.MediaPreprocessor>();

        // 🔧 新增：消息队列监控服务
        services.AddHostedService<HappyWechat.Infrastructure.MessageQueue.MessageQueueMonitorService>();

        // 🚀 新架构：注册消息处理流水线（修复生命周期冲突）
        services.AddScoped<HappyWechat.Infrastructure.MessageProcessing.Routing.IMessageTypeRouter,
            HappyWechat.Infrastructure.MessageProcessing.Routing.MessageTypeRouter>();
        services.AddScoped<HappyWechat.Infrastructure.MessageProcessing.Coordination.IMessageCoordinator,
            HappyWechat.Infrastructure.MessageProcessing.Coordination.MessageCoordinator>();
        services.AddScoped<HappyWechat.Infrastructure.MessageProcessing.Handlers.IStreamingAiResponseHandler,
            HappyWechat.Infrastructure.MessageProcessing.Handlers.StreamingAiResponseHandler>();

        // 🚀 统一架构：专用队列已整合到UnifiedMessageQueue中

        // 🚀 新架构：注册其他核心服务（修复生命周期冲突）
        services.AddScoped<HappyWechat.Infrastructure.MessageProcessing.Isolation.IAccountResourceManager,
            HappyWechat.Infrastructure.MessageProcessing.Isolation.AccountResourceManager>();
        services.AddScoped<HappyWechat.Infrastructure.MessageProcessing.GroupMode.IGroupModeProcessor,
            HappyWechat.Infrastructure.MessageProcessing.GroupMode.GroupModeProcessor>();
        services.AddSingleton<HappyWechat.Infrastructure.MessageProcessing.Recovery.ICircuitBreakerService,
            HappyWechat.Infrastructure.MessageProcessing.Recovery.CircuitBreakerService>();

        // 🚀 新架构：注册监控服务
        services.AddSingleton<HappyWechat.Infrastructure.MessageProcessing.Monitoring.IPipelineMonitor,
            HappyWechat.Infrastructure.MessageProcessing.Monitoring.PipelineMonitor>();
        services.AddHostedService<HappyWechat.Infrastructure.MessageProcessing.Monitoring.PipelineMonitor>();

        // 🚀 新架构：注册统一发送队列
        services.AddSingleton<HappyWechat.Infrastructure.MessageQueue.Unified.IUnifiedEYunSendQueue,
            HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedEYunSendQueue>();
        services.AddHostedService<HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedEYunSendQueueConsumer>();

        // 🚀 新架构：账号级别资源管理器
        services.AddSingleton<HappyWechat.Infrastructure.MessageQueue.Unified.IAccountResourceManager,
            HappyWechat.Infrastructure.MessageQueue.Unified.AccountResourceManager>();
            
        // 🚀 新架构：统一架构验证器
        services.AddScoped<HappyWechat.Infrastructure.MessageQueue.Unified.IUnifiedArchitectureValidator,
            HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedArchitectureValidator>();

        // 🚀 新架构：统一消息队列
        services.AddScoped<IUnifiedMessageQueue,
            HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedMessageQueue>();

        // 🚀 关键修复：统一消息消费者 - 处理AI请求和媒体请求的核心服务
        services.AddHostedService<HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedMessageConsumer>();

        // 🚀 核心统一架构服务
        services.AddScoped<IEYunCallbackProcessor, EYunCallbackProcessor>();
        services.AddScoped<IMessageTypeFilter, MessageTypeFilter>();
        services.AddScoped<IUnifiedConfigService, UnifiedConfigService>();
        
        // 🚀 统一架构整合服务（删除重复的旧服务）
        services.AddScoped<IUnifiedArchitectureIntegrationService, UnifiedArchitectureIntegrationService>();
        
        // 🚀 补充缺失的消息处理服务注册
        services.AddScoped<HappyWechat.Infrastructure.MessageProcessing.Services.IEnhancedGroupAiProcessor, 
            HappyWechat.Infrastructure.MessageProcessing.Services.EnhancedGroupAiProcessor>();
        services.AddScoped<HappyWechat.Infrastructure.MessageProcessing.Services.IPrivateMessageProcessor, 
            HappyWechat.Infrastructure.MessageProcessing.Services.PrivateMessageProcessor>();
        services.AddScoped<HappyWechat.Infrastructure.MessageProcessing.Services.IEnhancedGroupMessageProcessor, 
            HappyWechat.Infrastructure.MessageProcessing.Services.EnhancedGroupMessageProcessor>();
        services.AddScoped<HappyWechat.Infrastructure.MessageProcessing.Services.IContactAiConfigChecker, 
            HappyWechat.Infrastructure.MessageProcessing.Services.ContactAiConfigChecker>();
        
        // 🚀 统一架构：媒体处理服务（整合）
        services.AddScoped<HappyWechat.Infrastructure.MediaProcessing.IMediaProcessingRetryService,
            HappyWechat.Infrastructure.MediaProcessing.MediaProcessingRetryService>();
        services.AddScoped<HappyWechat.Infrastructure.MediaProcessing.IMediaProcessingFallbackService,
            HappyWechat.Infrastructure.MediaProcessing.MediaProcessingFallbackService>();
            
        // 🚀 流式架构：媒体转AI处理器 - 专门处理媒体消息AI转换
        services.AddScoped<HappyWechat.Infrastructure.MessageProcessing.Unified.IMediaToAiProcessor,
            HappyWechat.Infrastructure.MessageProcessing.Unified.MediaToAiProcessor>();
            
        // 🚀 流式架构：统一消息处理器 - 整合所有消息处理入口
        services.AddScoped<HappyWechat.Infrastructure.MessageProcessing.Unified.IUnifiedMessageProcessor,
            HappyWechat.Infrastructure.MessageProcessing.Unified.UnifiedMessageProcessor>();
            
        // 🚀 流式架构：好友请求处理器 - 智能好友管理
        services.AddScoped<HappyWechat.Infrastructure.MessageProcessing.Services.IFriendRequestProcessor,
            HappyWechat.Infrastructure.MessageProcessing.Services.FriendRequestProcessor>();
        
        // 🚀 统一架构：消息组合服务
        services.AddScoped<IMessageCombinationService, 
            HappyWechat.Infrastructure.MessageProcessing.Services.MessageCombinationService>();
        
        // 🚀 统一架构：简化的监控服务（删除冗余监控）
        services.AddSingleton<AiPerformanceMonitor>();
        services.AddSingleton<MessageDeliveryMonitor>();
        services.AddScoped<IPerformanceMonitor, PerformanceMonitor>();

        return services;
    }

    /// <summary>
    /// 移除旧架构的服务注册（清理冗余）
    /// </summary>
    public static IServiceCollection RemoveOldMessageQueueServices(this IServiceCollection services)
    {
        // 移除旧的消息队列相关服务
        // 注意：这个方法在实际使用时需要小心，确保不会影响其他功能
        
        // 可以移除的旧服务类型（示例）：
        // - IMessagePipelineOrchestrator
        // - IRedisQueueService 
        // - RedisAiMessageConsumer
        // - RedisWxCallbackMessageConsumer
        // - 等等...
        
        return services;
    }
}