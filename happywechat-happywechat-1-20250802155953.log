[ENTRYPOINT] HappyWechat 容器启动脚本开始执行
[ENTRYPOINT] 当前用户: root (UID: 0)
[ENTRYPOINT] 以root用户运行，将修复权限后切换到应用用户
[ENTRYPOINT] 执行权限修复流程...
[ENTRYPOINT] 以root身份修复权限...
[ENTRYPOINT] 创建必要目录...
[ENTRYPOINT] 创建目录: /app/logs
[SUCCESS] 目录创建完成
[ENTRYPOINT] 数据保护目录设置为700权限: /app/.aspnet/DataProtection-Keys
[ENTRYPOINT] 数据保护备份目录已创建
[ENTRYPOINT] 数据保护目录设置为700权限: /app/.aspnet/DataProtection-Keys/escrow
[SUCCESS] 权限修复完成
[ENTRYPOINT] 切换到应用用户并启动应用
info: HappyWechat.Infrastructure.Configuration.EnvironmentVariableConfigurationProvider[0]
      🐳 Docker环境-构建数据库连接字符串: Server=172.19.0.2;Port=3306;Database=huakai
info: HappyWechat.Infrastructure.Configuration.EnvironmentVariableConfigurationProvider[0]
      🐳 Docker环境-构建Redis连接字符串: 172.19.0.3:6379
info: HappyWechat.Infrastructure.IdManagement.IdMappingHealthService[0]
      开始执行ID映射缓存预热...
info: HappyWechat.Infrastructure.IdManagement.UnifiedIdManager[0]
      开始预热活跃账号缓存...
info: HappyWechat.Infrastructure.MessageQueue.Streaming.StreamingMessageArchitecture[0]
      🚀 流式消息处理架构启动 - 三通道异步流水线模式
info: HappyWechat.Infrastructure.MessageQueue.Streaming.StreamingMessageArchitecture[0]
      🔄 启动 Priority 通道处理器 - 并发度: 4
info: HappyWechat.Infrastructure.MessageQueue.Streaming.StreamingMessageArchitecture[0]
      🔄 启动 Fast 通道处理器 - 并发度: 8
info: HappyWechat.Infrastructure.MessageQueue.MessageQueueMonitorService[0]
      🔍 消息队列监控服务启动
info: HappyWechat.Infrastructure.MessageQueue.Streaming.StreamingMessageArchitecture[0]
      🔄 启动 Slow 通道处理器 - 并发度: 16
info: HappyWechat.Infrastructure.MessageProcessing.Monitoring.PipelineMonitor[0]
      🔍 流水线监控服务启动
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedEYunSendQueueConsumer[0]
      🚀 统一EYun发送队列消费者启动
info: HappyWechat.Infrastructure.IdManagement.UnifiedIdManager[0]
      缓存预热完成 - 账号数: 1, 耗时: 1474ms
info: HappyWechat.Infrastructure.IdManagement.IdMappingHealthService[0]
      ID映射缓存预热完成 - 账号数: 1, 耗时: 1948ms
info: HappyWechat.Infrastructure.ServiceRegistration.ServiceHealthValidator[0]
      ✅ 启动验证成功完成，耗时: 1.8582ms
warn: Microsoft.AspNetCore.Hosting.Diagnostics[15]
      Overriding HTTP_PORTS '8080' and HTTPS_PORTS ''. Binding to values defined by URLS instead 'http://+:5215'.
info: StackExchange.Redis.Extensions.Core.Implementations.RedisConnectionPoolManager[0]
      Redis connection error restored
info: HappyWechat.Infrastructure.MessageProcessing.EYunCallbackProcessor[0]
      [d85ad6e0] 📥 收到EYun回调数据: FromUser: wxid_pbqye5z48yzm22, Content: 你好, MessageType: 60001
info: HappyWechat.Infrastructure.MessageQueue.Streaming.StreamingMessageArchitecture[0]
      📥 流式架构消息入队 - Channel: Fast, ProcessingId: d85ad6e0, MessageType: 60001, StreamingType: TextFast
info: HappyWechat.Infrastructure.MessageProcessing.EYunCallbackProcessor[0]
      [d85ad6e0] ✅ 流式架构路由完成 - Duration: 67.0728ms, Result: 消息已路由到PrivateTextFast通道
info: HappyWechat.Infrastructure.MessageProcessing.EYunCallbackProcessor[0]
      [d85ad6e0] ✅ 回调数据处理完成 - MessageType: 60001, WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144
info: HappyWechat.Web.Controllers.WxController[0]
      [be827b52] ✅ 回调处理成功
info: HappyWechat.Infrastructure.MessageProcessing.EYunCallbackProcessor[0]
      [f6170850] 📥 收到EYun回调数据: FromUser: wxid_ic3nmv9anggz22, MessageType: 60999
info: HappyWechat.Web.Controllers.WxController[0]
      [21cc6fbf] ✅ 回调处理成功
info: HappyWechat.Infrastructure.Auth.SessionIdRetrievalService[0]
      🔧 SessionId获取服务已初始化，策略数量: 4
info: HappyWechat.Infrastructure.Auth.SessionIdRetrievalService[0]
      🔧 SessionId获取服务已初始化，策略数量: 4
info: HappyWechat.Infrastructure.Auth.SessionIdRetrievalService[0]
      ✅ SessionId获取成功 - 策略: HTTP上下文策略, SessionId: 9ce43737..., 耗时: 1ms
info: HappyWechat.Infrastructure.Auth.SessionIdRetrievalService[0]
      🔧 SessionId获取服务已初始化，策略数量: 4
info: HappyWechat.Infrastructure.MessageProcessing.EYunCallbackProcessor[0]
      [b9d194a7] 📥 收到EYun回调数据: FromUser: wxid_scqpt8dyuxqv41, MessageType: 60004
info: HappyWechat.Infrastructure.MessageQueue.Streaming.StreamingMessageArchitecture[0]
      📥 流式架构消息入队 - Channel: Slow, ProcessingId: b9d194a7, MessageType: 60004, StreamingType: MediaSlow
info: HappyWechat.Infrastructure.MessageProcessing.EYunCallbackProcessor[0]
      [b9d194a7] ✅ 流式架构路由完成 - Duration: 6.4397ms, Result: 消息已路由到MediaSlow通道
info: HappyWechat.Infrastructure.MessageProcessing.EYunCallbackProcessor[0]
      [b9d194a7] ✅ 回调数据处理完成 - MessageType: 60004, WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144
info: HappyWechat.Web.Controllers.WxController[0]
      [09f3ec76] ✅ 回调处理成功
info: HappyWechat.Infrastructure.MessageProcessing.EYunCallbackProcessor[0]
      [89e179db] 📥 收到EYun回调数据: FromUser: wxid_scqpt8dyuxqv41, MessageType: 60008
info: HappyWechat.Web.Controllers.WxController[0]
      [303e602e] ✅ 回调处理成功
info: HappyWechat.Infrastructure.MessageProcessing.EYunCallbackProcessor[0]
      [d9218a55] 📥 收到EYun回调数据: FromUser: wxid_scqpt8dyuxqv41, MessageType: 60002
info: HappyWechat.Infrastructure.MessageQueue.Streaming.StreamingMessageArchitecture[0]
      📥 流式架构消息入队 - Channel: Slow, ProcessingId: d9218a55, MessageType: 60002, StreamingType: MediaSlow
info: HappyWechat.Infrastructure.MessageProcessing.EYunCallbackProcessor[0]
      [d9218a55] ✅ 流式架构路由完成 - Duration: 7.0669ms, Result: 消息已路由到MediaSlow通道
info: HappyWechat.Infrastructure.MessageProcessing.EYunCallbackProcessor[0]
      [d9218a55] ✅ 回调数据处理完成 - MessageType: 60002, WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144
info: HappyWechat.Web.Controllers.WxController[0]
      [9e0c0fb5] ✅ 回调处理成功
